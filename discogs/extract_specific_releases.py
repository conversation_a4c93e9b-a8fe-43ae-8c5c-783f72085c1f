#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import re
import os
import csv
import json
import glob
from datetime import datetime, timezone

# 导入枚举值
try:
    from release.enums import Permissions, Status, Source
except ImportError:
    # 如果相对导入失败，尝试直接导入
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), 'release'))
    from enums import Permissions, Status, Source

# 目标ID列表
TARGET_IDS = [32, 33, 34, 49, 58, 104, 106, 108, 122, 134, 142, 167, 182, 193, 201, 213, 225, 226, 228, 245]

# 配置参数
CSV_OUTPUT_FILE = "releases_specific_ids_20250701.csv"

def find_xml_file(module_type):
    """
    查找指定模块的XML文件，优先搜索当前目录
    
    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')
    
    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 首先在当前目录搜索（支持.xml和.xml.gz格式）
    current_dir_patterns = [
        f'*_{module_type}.xml.gz',
        f'*_{module_type}.xml',
        f'discogs_*_{module_type}.xml.gz',
        f'discogs_*_{module_type}.xml'
    ]
    
    found_files = []
    for pattern in current_dir_patterns:
        found_files.extend(glob.glob(pattern))
    
    if found_files:
        # 如果找到多个文件，选择最新的（按文件名排序）
        if len(found_files) > 1:
            found_files.sort()
            selected_file = found_files[-1]
            print(f"🔍 在当前目录找到多个文件，选择最新的: {selected_file}")
        else:
            selected_file = found_files[0]
            print(f"✅ 在当前目录检测到文件: {selected_file}")
        return selected_file
    
    print(f"❌ 未找到 {module_type} 模块的XML文件")
    return None

def write_output(message, print_to_console=True):
    """将消息写入控制台"""
    if print_to_console:
        print(message)

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    # 支持带属性的标签，如 <master_id attr="value">content</master_id>
    pattern = f'<{field_name}[^>]*>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else None

def extract_attribute(content, tag_name, attribute_name):
    """从XML内容中提取指定标签的属性值"""
    # 匹配标签及其属性，支持多种属性顺序
    pattern = f'<{tag_name}[^>]*{attribute_name}="([^"]*)"[^>]*>'
    match = re.search(pattern, content)
    return match.group(1).strip() if match else None

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def extract_artists(content):
    """提取主要艺术家字段 (artists)"""
    artists = []

    # 提取主要艺术家 (artists)
    artists_match = re.search(r'<artists>(.*?)</artists>', content, re.DOTALL)
    if artists_match:
        artists_content = artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            # 支持 <name> 和 <n> 两种标签格式
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Primary"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                artists.append(artist_doc)

    return artists

def extract_extra_artists(content):
    """提取额外艺术家字段 (extra_artists)"""
    extra_artists = []

    # 提取额外艺术家 (extraartists)
    extra_artists_match = re.search(r'<extraartists>(.*?)</extraartists>', content, re.DOTALL)
    if extra_artists_match:
        extra_artists_content = extra_artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, extra_artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            # 支持 <name> 和 <n> 两种标签格式
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Unknown"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                extra_artists.append(artist_doc)

    return extra_artists

def extract_labels(content):
    """提取labels字段"""
    labels = []
    labels_match = re.search(r'<labels>(.*?)</labels>', content, re.DOTALL)
    if not labels_match:
        return labels
    
    labels_content = labels_match.group(1)
    label_pattern = r'<label[^>]*name="([^"]*)"[^>]*catno="([^"]*)"[^>]*id="([^"]*)"[^>]*/?>'
    label_matches = re.findall(label_pattern, labels_content)
    
    for name, catno, label_id in label_matches:
        if name:
            labels.append({
                'name': name,
                'catno': catno,
                'id': label_id
            })
    
    return labels

def extract_companies(content):
    """提取companies字段"""
    companies = []
    companies_match = re.search(r'<companies>(.*?)</companies>', content, re.DOTALL)
    if not companies_match:
        return companies
    
    companies_content = companies_match.group(1)
    company_pattern = r'<company>(.*?)</company>'
    company_matches = re.findall(company_pattern, companies_content, re.DOTALL)
    
    for company_content in company_matches:
        company_id = extract_field(company_content, 'id')
        name = extract_field(company_content, 'name')
        catno = extract_field(company_content, 'catno')
        entity_type = extract_field(company_content, 'entity_type')
        entity_type_name = extract_field(company_content, 'entity_type_name')
        
        if company_id and name:
            companies.append({
                'id': company_id,
                'name': name,
                'catno': catno,
                'entity_type': entity_type,
                'entity_type_name': entity_type_name
            })
    
    return companies

def extract_formats(content):
    """提取formats字段"""
    formats = []
    formats_match = re.search(r'<formats>(.*?)</formats>', content, re.DOTALL)
    if not formats_match:
        return formats
    
    formats_content = formats_match.group(1)
    format_pattern = r'<format[^>]*name="([^"]*)"[^>]*qty="([^"]*)"[^>]*text="([^"]*)"[^>]*>(.*?)</format>'
    format_matches = re.findall(format_pattern, formats_content, re.DOTALL)
    
    for name, qty, text, format_inner in format_matches:
        format_doc = {
            'name': name,
            'qty': qty,
            'text': text,
            'descriptions': []
        }
        
        # 提取descriptions
        desc_pattern = r'<description>([^<]*)</description>'
        descriptions = re.findall(desc_pattern, format_inner)
        format_doc['descriptions'] = descriptions
        
        formats.append(format_doc)
    
    return formats

def extract_list_field(content, field_name, item_name):
    """提取列表字段（如genres, styles）"""
    items = []
    field_match = re.search(f'<{field_name}>(.*?)</{field_name}>', content, re.DOTALL)
    if not field_match:
        return items
    
    field_content = field_match.group(1)
    item_pattern = f'<{item_name}>([^<]*)</{item_name}>'
    items = re.findall(item_pattern, field_content)
    
    return items

def extract_tracklist(content):
    """提取tracklist字段"""
    tracklist = []
    tracklist_match = re.search(r'<tracklist>(.*?)</tracklist>', content, re.DOTALL)
    if not tracklist_match:
        return tracklist
    
    tracklist_content = tracklist_match.group(1)
    track_pattern = r'<track>(.*?)</track>'
    track_matches = re.findall(track_pattern, tracklist_content, re.DOTALL)
    
    for track_content in track_matches:
        position = extract_field(track_content, 'position')
        title = extract_field(track_content, 'title')
        duration = extract_field(track_content, 'duration')
        
        track_doc = {
            'position': position,
            'title': title,
            'duration': duration,
            'artists': []
        }
        
        # 提取track中的艺术家
        track_artists_match = re.search(r'<artists>(.*?)</artists>', track_content, re.DOTALL)
        if track_artists_match:
            track_artists_content = track_artists_match.group(1)
            track_artist_pattern = r'<artist>(.*?)</artist>'
            track_artist_matches = re.findall(track_artist_pattern, track_artists_content, re.DOTALL)
            
            for track_artist_content in track_artist_matches:
                artist_id = extract_field(track_artist_content, 'id')
                name = extract_field(track_artist_content, 'name') or extract_field(track_artist_content, 'n')
                anv = extract_field(track_artist_content, 'anv')
                role = extract_field(track_artist_content, 'role') or "Primary"
                
                if artist_id and name:
                    track_artist_doc = {
                        'artist_id': int(artist_id),
                        'name': name,
                        'role': role
                    }
                    if anv:
                        track_artist_doc['anv'] = anv
                    track_doc['artists'].append(track_artist_doc)
        
        tracklist.append(track_doc)
    
    return tracklist

def format_array_for_csv(data_list):
    """将列表数据格式化为JSON数组格式，用于CSV存储"""
    if not data_list:
        return "[]"
    if isinstance(data_list, list):
        return json.dumps(data_list, ensure_ascii=False)
    return json.dumps([data_list], ensure_ascii=False)

def write_releases_to_csv(release_docs, csv_filename):
    """将release数据写入CSV文件"""
    if not release_docs:
        print("没有数据需要写入CSV文件")
        return

    # 定义CSV字段（基于process_releases.py的字段结构）
    fieldnames = [
        'id', 'y_id', 'title', 'artists', 'extra_artists', 'labels', 'companies',
        'country', 'formats', 'genres', 'styles', 'tracklist', 'master_id',
        'discogs_status', 'created_at', 'source', 'permissions'
    ]

    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for release_doc in release_docs:
                # 处理复杂字段，转换为适合CSV的格式
                csv_row = {}
                for field in fieldnames:
                    if field in release_doc:
                        value = release_doc[field]
                        if field in ['artists', 'extra_artists', 'labels', 'companies', 'formats', 'genres', 'styles', 'tracklist']:
                            # 数组字段转换为JSON格式
                            csv_row[field] = format_array_for_csv(value)
                        elif isinstance(value, datetime):
                            # 日期时间转换为字符串
                            csv_row[field] = value.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            csv_row[field] = value
                    else:
                        csv_row[field] = ''

                writer.writerow(csv_row)

        print(f"✅ 成功将 {len(release_docs)} 条记录写入CSV文件: {csv_filename}")

    except Exception as e:
        print(f"❌ 写入CSV文件时出错: {e}")
        raise

def process_release_content(buffer, yid_counter):
    """处理单个release标签的内容"""
    # 提取release ID
    release_id = extract_release_id(buffer)
    if not release_id:
        return None

    # 检查是否为目标ID
    if release_id not in TARGET_IDS:
        return None

    # 生成yId
    y_id = f"YRD{yid_counter}"

    # 提取 discogs_status
    discogs_status = extract_attribute(buffer, 'release', 'status') or 'unknown'

    # 创建release文档
    release_doc = {
        'y_id': y_id,
        'id': release_id,
        'title': extract_field(buffer, 'title'),

        # —— ARTISTS ——
        'artists': extract_artists(buffer),
        'extra_artists': extract_extra_artists(buffer),

        # —— LABELS & COMPANIES ——
        'labels': extract_labels(buffer),
        'companies': extract_companies(buffer),
        'country': extract_field(buffer, 'country'),

        # —— FORMATS / GENRES / STYLES ——
        'formats': extract_formats(buffer),
        'genres': extract_list_field(buffer, 'genres', 'genre'),
        'styles': extract_list_field(buffer, 'styles', 'style'),

        # —— TRACKLIST ——
        'tracklist': extract_tracklist(buffer),

        # —— 其余字段 ——
        'master_id': extract_field(buffer, 'master_id'),
        'permissions': Permissions.ALL_VISIBLE.value,
        'source': Source.DISCOGS.value,
        'discogs_status': discogs_status,

        'created_at': datetime.now(timezone.utc)
    }

    # 转换master_id为整数
    if release_doc['master_id']:
        try:
            release_doc['master_id'] = int(release_doc['master_id'])
        except ValueError:
            release_doc['master_id'] = None

    return release_doc

def process_specific_releases():
    """处理XML文件中的特定release记录"""
    start_time = time.time()

    # 查找XML文件
    xml_file = find_xml_file('releases')
    if not xml_file:
        print("❌ 错误: 无法找到 releases 模块的XML数据文件")
        print("请确保文件存在于当前目录，文件名格式: discogs_YYYYMMDD_releases.xml.gz")
        return

    # 转换目标ID为集合以提高查找效率
    target_ids_set = set(TARGET_IDS)
    found_releases = []
    target_ids_found = set()

    processed_count = 0
    total_records_scanned = 0
    yid_counter = 1

    try:
        write_output(f"开始处理XML文件: {xml_file}")
        write_output(f"目标ID列表: {TARGET_IDS}")
        write_output(f"目标ID数量: {len(TARGET_IDS)}")

        # 判断文件是否为gzip压缩格式
        if xml_file.endswith('.gz'):
            file_opener = lambda: gzip.open(xml_file, 'rt', encoding='utf-8')
        else:
            file_opener = lambda: open(xml_file, 'r', encoding='utf-8')

        # 打开XML文件并逐行读取
        with file_opener() as f:
            buffer = ""
            in_release = False

            for line in f:
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records_scanned += 1
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False

                    # 处理release内容
                    release_doc = process_release_content(buffer, yid_counter)
                    if release_doc:
                        found_releases.append(release_doc)
                        target_ids_found.add(release_doc['id'])
                        processed_count += 1
                        yid_counter += 1

                        # 记录详细日志
                        title_short = release_doc['title'][:50] if release_doc['title'] else "无标题"
                        log_message = (f"✅ 找到目标记录 {processed_count}: y_id={release_doc['y_id']}, "
                                      f"id={release_doc['id']}, title={title_short}...")
                        write_output(log_message)

                        # 如果找到所有目标ID，可以提前退出
                        if len(target_ids_found) == len(TARGET_IDS):
                            write_output(f"🎉 已找到所有目标ID，提前结束扫描")
                            break

                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line

                # 每扫描10000条记录显示一次进度
                if total_records_scanned % 10000 == 0:
                    write_output(f"🔄 已扫描 {total_records_scanned:,} 条记录，找到 {len(target_ids_found)} 个目标ID...")

        # 处理完成后写入CSV
        if found_releases:
            write_releases_to_csv(found_releases, CSV_OUTPUT_FILE)
        else:
            write_output("❌ 未找到任何目标ID的记录")

    except Exception as e:
        error_msg = f"❌ 处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        stats = [
            "\n" + "="*60,
            "处理结果统计",
            "="*60,
            f"目标ID列表: {TARGET_IDS}",
            f"找到的ID: {sorted(list(target_ids_found))}",
            f"未找到的ID: {sorted(list(set(TARGET_IDS) - target_ids_found))}",
            f"共扫描了 {total_records_scanned:,} 条记录",
            f"成功找到了 {processed_count} 条目标记录",
            f"找到率: {len(target_ids_found)}/{len(TARGET_IDS)} ({len(target_ids_found)/len(TARGET_IDS)*100:.1f}%)",
            f"处理时长: {processing_time:.2f} 秒",
            (f"平均扫描速度: {total_records_scanned/processing_time:.0f} 记录/秒"
             if processing_time > 0 else "平均扫描速度: 0 记录/秒"),
            f"CSV输出文件: {CSV_OUTPUT_FILE}",
            "="*60
        ]

        # 打印统计信息
        for stat in stats:
            write_output(stat)

if __name__ == "__main__":
    process_specific_releases()
